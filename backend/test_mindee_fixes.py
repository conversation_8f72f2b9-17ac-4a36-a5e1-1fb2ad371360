#!/usr/bin/env python3
"""
Test script to verify the Mindee service fixes for date and quantity extraction.
"""

import os
import sys
import logging

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.mindee_service import MindeeService

# Configure logging to see debug output
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_mindee_service():
    """Test the Mindee service with the uploaded image."""
    
    print("Testing Mindee service with fixes...")
    print("=" * 50)
    
    # Initialize the service
    mindee_service = MindeeService()
    
    # Check if we have an API key
    if not mindee_service.api_key:
        print("⚠ No Mindee API key found. Testing with mock data...")
        result = mindee_service._mock_response()
        print("Mock response:")
        print(f"  Invoice Date: {result.get('invoice_date')}")
        print(f"  Total Amount: {result.get('total_amount')}")
        print(f"  Items: {len(result.get('items', []))}")
        for i, item in enumerate(result.get('items', [])[:3]):  # Show first 3 items
            print(f"    Item {i+1}: {item.get('description')} - Qty: {item.get('quantity')} - Price: {item.get('unit_price')}")
        return
    
    # Test with the uploaded image
    image_path = "uploads/6d62e3be-3768-4abe-bc07-e6da3e751a3a_IMG_1468.jpeg"
    
    if not os.path.exists(image_path):
        print(f"⚠ Test image not found: {image_path}")
        return
    
    print(f"Testing with image: {image_path}")
    
    # Test with different vendors
    vendors = ['bova', 'kast', 'generic']
    
    for vendor in vendors:
        print(f"\n--- Testing {vendor.upper()} vendor ---")
        try:
            result = mindee_service.process_invoice(image_path, vendor=vendor)
            
            if result.get('success'):
                print(f"✓ {vendor} processing successful")
                print(f"  Invoice Date: {result.get('invoice_date')}")
                print(f"  Total Amount: {result.get('total_amount')}")
                print(f"  Items: {len(result.get('items', []))}")
                
                # Show first few items with quantities
                for i, item in enumerate(result.get('items', [])[:3]):  # Show first 3 items
                    qty = item.get('quantity', 0)
                    print(f"    Item {i+1}: {item.get('description', 'N/A')[:50]}... - Qty: {qty} - Price: {item.get('unit_price')}")
                    
                    # Check if quantity is properly parsed (not 0 when it should have a value)
                    if qty > 0:
                        print(f"      ✓ Quantity properly parsed: {qty}")
                    elif qty == 0:
                        print(f"      ⚠ Quantity is 0 - may need investigation")
                        
            else:
                print(f"✗ {vendor} processing failed: {result.get('error')}")
                
        except Exception as e:
            print(f"✗ {vendor} processing error: {str(e)}")
        
        print()

if __name__ == "__main__":
    test_mindee_service()
