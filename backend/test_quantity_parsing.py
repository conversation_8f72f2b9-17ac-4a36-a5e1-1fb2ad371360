#!/usr/bin/env python3
"""
Test script to verify quantity parsing fixes in Mindee service.
"""

import re

def test_quantity_parsing():
    """Test the quantity parsing logic."""
    
    test_quantities = [
        "1 CS",
        "2 EACH", 
        "3 BG",
        "1.5 LB",
        "10",
        "5.25",
        "0.5 KG",
        "12 BOXES",
        "",
        None,
        "INVALID",
        "NO_NUMBER_HERE"
    ]
    
    print("Testing quantity parsing logic:")
    print("=" * 50)
    
    for quantity_val in test_quantities:
        try:
            if quantity_val:
                # Handle quantity strings like "1 CS", "2 EACH", "3 BG", etc.
                quantity_str = str(quantity_val).strip()
                print(f"Processing quantity string: '{quantity_str}'")
                
                # Extract numeric part from quantity string
                # Match numbers (including decimals) at the beginning of the string
                match = re.match(r'^(\d+(?:\.\d+)?)', quantity_str)
                if match:
                    quantity_num = float(match.group(1))
                    print(f"  ✓ Extracted numeric quantity: {quantity_num} from '{quantity_str}'")
                    quantity = quantity_num
                else:
                    print(f"  ⚠ Could not extract numeric quantity from '{quantity_str}', defaulting to 1")
                    quantity = 1  # Default to 1 if we can't parse
            else:
                quantity = 0
                print(f"  ✓ Empty/None quantity, set to 0")
        except (ValueError, TypeError) as e:
            print(f"  ✗ Error converting quantity '{quantity_val}': {e}, defaulting to 0")
            quantity = 0
        
        print(f"  Final quantity: {quantity}")
        print()

if __name__ == "__main__":
    test_quantity_parsing()
