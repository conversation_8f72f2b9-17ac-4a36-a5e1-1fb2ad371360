#!/usr/bin/env python3
"""
Test script to verify date extraction is working correctly.
"""

import json

def test_date_extraction():
    """Test the date extraction logic with mock data."""
    
    # Mock Bova response data (based on actual logs)
    mock_bova_prediction = {
        'due_date': {'value': None},
        'invoice_date': {'value': '2025-03-18'},
        'invoice_number': {'value': '01750044'},
        'please_pay': {'value': 3085.92},
        'prior_amount_due': {'value': 3132.25},
        'line_items': []
    }
    
    # Mock Kast response data (based on actual logs)
    mock_kast_prediction = {
        'customer_email': {'value': '<EMAIL>'},
        'order_date': {'value': '2025-01-23'},
        'order_number': {'value': 'ORD123'},
        'total_amount': {'value': 2141.9},
        'line_items': []
    }
    
    def _get_field_value(prediction, field_name):
        """Mock the _get_field_value method."""
        if field_name in prediction:
            field = prediction[field_name]
            if isinstance(field, dict):
                if 'value' in field:
                    return field.get('value', '')
                elif 'Value' in field:
                    return field.get('Value', '')
                else:
                    return field
            else:
                return field
        else:
            return ''
    
    print("Testing Date Extraction Logic:")
    print("=" * 50)
    
    # Test Bova date extraction
    print("\n--- BOVA DATE EXTRACTION ---")
    print(f"Available fields: {list(mock_bova_prediction.keys())}")
    
    # Check date-like fields
    date_like_fields = [f for f in mock_bova_prediction.keys() if any(keyword in f.lower() for keyword in ['date', 'time', 'day', 'month', 'year'])]
    print(f"Date-like fields: {date_like_fields}")
    
    for field in date_like_fields:
        field_value = _get_field_value(mock_bova_prediction, field)
        print(f"  {field} = {field_value}")
    
    # Test primary date extraction
    bova_date = _get_field_value(mock_bova_prediction, 'invoice_date')
    print(f"\nBova extracted date: '{bova_date}'")
    
    if bova_date:
        print("✅ Bova date extraction: SUCCESS")
    else:
        print("❌ Bova date extraction: FAILED")
    
    # Test Kast date extraction
    print("\n--- KAST DATE EXTRACTION ---")
    print(f"Available fields: {list(mock_kast_prediction.keys())}")
    
    # Check date-like fields
    date_like_fields = [f for f in mock_kast_prediction.keys() if any(keyword in f.lower() for keyword in ['date', 'time', 'day', 'month', 'year'])]
    print(f"Date-like fields: {date_like_fields}")
    
    for field in date_like_fields:
        field_value = _get_field_value(mock_kast_prediction, field)
        print(f"  {field} = {field_value}")
    
    # Test primary date extraction (order_date for Kast)
    kast_date = _get_field_value(mock_kast_prediction, 'order_date')
    print(f"\nKast extracted date: '{kast_date}'")
    
    if kast_date:
        print("✅ Kast date extraction: SUCCESS")
    else:
        print("❌ Kast date extraction: FAILED")
    
    # Test final response structure
    print("\n--- FINAL RESPONSE STRUCTURE ---")
    
    bova_result = {
        "success": True,
        "invoice_number": _get_field_value(mock_bova_prediction, 'invoice_number'),
        "invoice_date": bova_date,  # This should contain the date
        "total_amount": _get_field_value(mock_bova_prediction, 'please_pay'),
        "supplier_name": "Bova",
        "items": []
    }
    
    kast_result = {
        "success": True,
        "invoice_number": _get_field_value(mock_kast_prediction, 'order_number'),
        "invoice_date": kast_date,  # This should contain the date
        "total_amount": _get_field_value(mock_kast_prediction, 'total_amount'),
        "supplier_name": "Kast",
        "items": []
    }
    
    print("Bova final result:")
    print(json.dumps(bova_result, indent=2))
    
    print("\nKast final result:")
    print(json.dumps(kast_result, indent=2))
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY:")
    
    if bova_result['invoice_date'] and kast_result['invoice_date']:
        print("✅ ALL DATE EXTRACTIONS WORKING CORRECTLY!")
        print(f"  Bova date: {bova_result['invoice_date']}")
        print(f"  Kast date: {kast_result['invoice_date']}")
    else:
        print("❌ Some date extractions are failing:")
        if not bova_result['invoice_date']:
            print("  - Bova date extraction failed")
        if not kast_result['invoice_date']:
            print("  - Kast date extraction failed")

if __name__ == "__main__":
    test_date_extraction()
