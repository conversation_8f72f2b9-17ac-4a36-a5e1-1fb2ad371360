#!/usr/bin/env python3
"""
Direct test of the quantity parsing fix without Flask dependencies.
"""

import re
import json

def test_quantity_parsing_fix():
    """Test the quantity parsing logic directly."""
    
    # Mock line item data similar to what <PERSON><PERSON> returns
    mock_line_items = [
        {"item": "BOVA RICOTTA WHOLE MILK", "quantity_shipped": "2 CS", "selling_price": "17.5", "combined_price": "35"},
        {"item": "CAPRA GOAT CHEESE LOG", "quantity_shipped": "1 EACH", "selling_price": "66.68", "combined_price": "66.68"},
        {"item": "OCEANSPRAY CRAISINS", "quantity_shipped": "3 BG", "selling_price": "13.3", "combined_price": "39.9"},
        {"item": "ROYAL SUGAR", "quantity_shipped": "1.5 LB", "selling_price": "39.5", "combined_price": "59.25"},
        {"item": "CENTO SOUP", "quantity_shipped": "10", "selling_price": "19.99", "combined_price": "199.9"},
    ]
    
    print("Testing quantity parsing fix directly:")
    print("=" * 60)
    
    processed_items = []
    
    for item in mock_line_items:
        description = item.get("item", "")
        quantity_raw = item.get("quantity_shipped", "")
        unit_price_raw = item.get("selling_price", "")
        amount_raw = item.get("combined_price", "")
        
        print(f"\nProcessing item: {description}")
        print(f"  Raw quantity: '{quantity_raw}'")
        
        # Apply the same logic as in our fix
        try:
            if quantity_raw:
                # Handle quantity strings like "1 CS", "2 EACH", "3 BG", etc.
                quantity_str = str(quantity_raw).strip()
                print(f"  Processing quantity string: '{quantity_str}'")
                
                # Extract numeric part from quantity string
                # Match numbers (including decimals) at the beginning of the string
                match = re.match(r'^(\d+(?:\.\d+)?)', quantity_str)
                if match:
                    quantity_num = float(match.group(1))
                    print(f"  ✓ Extracted numeric quantity: {quantity_num} from '{quantity_str}'")
                    quantity = quantity_num
                else:
                    print(f"  ⚠ Could not extract numeric quantity from '{quantity_str}', defaulting to 1")
                    quantity = 1  # Default to 1 if we can't parse
            else:
                quantity = 0
        except (ValueError, TypeError) as e:
            print(f"  ✗ Error converting quantity '{quantity_raw}': {e}, defaulting to 0")
            quantity = 0
        
        # Convert other fields
        try:
            unit_price = float(unit_price_raw) if unit_price_raw else 0
        except (ValueError, TypeError):
            unit_price = 0
            
        try:
            amount = float(amount_raw) if amount_raw else 0
        except (ValueError, TypeError):
            amount = 0
        
        processed_item = {
            'description': description,
            'quantity': quantity,
            'unit_price': unit_price,
            'amount': amount
        }
        
        processed_items.append(processed_item)
        
        print(f"  Final result: qty={quantity}, price=${unit_price}, amount=${amount}")
        
        # Verify the fix worked
        if quantity > 0:
            print(f"  ✓ SUCCESS: Quantity properly parsed!")
        else:
            print(f"  ✗ ISSUE: Quantity is still 0")
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"Total items processed: {len(processed_items)}")
    
    successful_quantities = [item for item in processed_items if item['quantity'] > 0]
    print(f"Items with successful quantity parsing: {len(successful_quantities)}")
    
    if len(successful_quantities) == len(processed_items):
        print("✓ ALL QUANTITY PARSING TESTS PASSED!")
    else:
        print(f"⚠ {len(processed_items) - len(successful_quantities)} items still have quantity issues")
    
    print("\nProcessed items:")
    for i, item in enumerate(processed_items, 1):
        print(f"  {i}. {item['description'][:30]}... - Qty: {item['quantity']} - Price: ${item['unit_price']}")

if __name__ == "__main__":
    test_quantity_parsing_fix()
