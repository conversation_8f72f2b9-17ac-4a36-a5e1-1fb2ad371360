#!/usr/bin/env python3
"""
Test script to verify the date format fix for OCR responses.
"""

import json
from datetime import date, datetime

def test_date_serialization():
    """Test how different date formats are serialized to JSON."""
    
    print("Testing Date Serialization for OCR Response:")
    print("=" * 60)
    
    # Test different date formats
    test_date = date(2025, 3, 18)
    test_datetime = datetime(2025, 3, 18, 14, 30, 0)
    
    print(f"Original Python date object: {test_date}")
    print(f"Original Python datetime object: {test_datetime}")
    print()
    
    # Test 1: Direct JSON serialization (this would fail)
    print("--- Test 1: Direct JSON serialization ---")
    try:
        json_str = json.dumps({"date": test_date})
        print(f"✗ This should fail: {json_str}")
    except TypeError as e:
        print(f"✓ Expected error: {e}")
    
    # Test 2: ISO format conversion (our fix)
    print("\n--- Test 2: ISO format conversion (our fix) ---")
    iso_date = test_date.isoformat()
    print(f"ISO format string: {iso_date}")
    json_str = json.dumps({"invoice_date": iso_date})
    print(f"✓ JSON serialization successful: {json_str}")
    
    # Test 3: Verify the format is correct for HTML date input
    print("\n--- Test 3: HTML date input compatibility ---")
    parsed_data = json.loads(json_str)
    date_value = parsed_data["invoice_date"]
    print(f"Date value from JSON: '{date_value}'")
    
    # Check if it matches YYYY-MM-DD format
    import re
    if re.match(r'^\d{4}-\d{2}-\d{2}$', date_value):
        print("✓ Date format is compatible with HTML date input (YYYY-MM-DD)")
    else:
        print("✗ Date format is NOT compatible with HTML date input")
    
    # Test 4: Mock OCR response structure
    print("\n--- Test 4: Mock OCR response structure ---")
    mock_ocr_response = {
        "message": "OCR processing completed successfully",
        "invoice": {
            "invoice_number": "01750044",
            "vendor_name": "Bova",
            "invoice_date": test_date.isoformat(),  # Our fix
            "total_amount": 3085.92,
            "line_items": [
                {
                    "description": "Test Item",
                    "quantity": 2.0,
                    "unit_price": 17.5,
                    "amount": 35.0
                }
            ]
        },
        "success": True
    }
    
    # Serialize to JSON
    json_response = json.dumps(mock_ocr_response, indent=2)
    print("Mock OCR response JSON:")
    print(json_response)
    
    # Test parsing on frontend side
    print("\n--- Test 5: Frontend parsing simulation ---")
    parsed_response = json.loads(json_response)
    frontend_date = parsed_response["invoice"]["invoice_date"]
    print(f"Frontend receives date: '{frontend_date}'")
    
    # Simulate HTML date input value assignment
    print(f"HTML input value would be: value=\"{frontend_date}\"")
    
    if frontend_date and re.match(r'^\d{4}-\d{2}-\d{2}$', frontend_date):
        print("✅ SUCCESS: Date will display correctly in HTML date input!")
    else:
        print("❌ FAILURE: Date will show as placeholder 'mm/dd/yyyy'")
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("✅ Date format fix implemented: Convert date.isoformat() before JSON response")
    print("✅ HTML date input compatibility: YYYY-MM-DD format")
    print("✅ Frontend will receive properly formatted date string")

if __name__ == "__main__":
    test_date_serialization()
